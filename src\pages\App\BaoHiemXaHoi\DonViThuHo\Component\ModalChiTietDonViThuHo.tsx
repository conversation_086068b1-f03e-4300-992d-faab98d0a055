import {ArrowLeftOutlined, CheckOutlined, FolderOpenOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useRef, useState} from "react";
import {FormTaoMoiDonViThuHo, TRANG_THAI_TAO_MOI_DON_VI_THU_HO} from "../index.configs";
import {useQuanLyDonViThuHoContext} from "../index.context";
import {ChiTietDonViThuHoProps, IModalChiTietDonViThuHoRef} from "./Constant";
import "../index.default.scss";
import {env} from "@src/utils";
import {ModalChonQRCode} from "./ModalChonQRCode";
const {ma, ten, ten_tat, ten_e, dchi, dthoai, mst, stt, trang_thai, ma_nh, so_tk, ten_tk, file_qrcode, id_file_qrcode} = FormTaoMoiDonViThuHo;

const ModalChiTietDonViThuHoComponent = forwardRef<IModalChiTietDonViThuHoRef, ChiTietDonViThuHoProps>(({listDonViThuHo}: ChiTietDonViThuHoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDonViThuHo?: CommonExecute.Execute.IDonViThuHo) => {
      setIsOpen(true);
      if (dataDonViThuHo) setChiTietDonViThuHo(dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietDonViThuHo, setChiTietDonViThuHo] = useState<CommonExecute.Execute.IDonViThuHo | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  const {capNhatChiTietDonViThuHo, getListDonViThuHo, loading, listNganHang} = useQuanLyDonViThuHoContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const modalQuanLyFileRef = useRef<any>(null);
  const modalChonQRCodeRef = useRef<any>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietDonViThuHo) {
      const arrFormData = [];
      for (const key in chiTietDonViThuHo) {
        arrFormData.push({
          name: key,
          value: chiTietDonViThuHo[key as keyof CommonExecute.Execute.IDonViThuHo],
        });
      }
      form.setFields(arrFormData);
    }
  }, [chiTietDonViThuHo]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietDonViThuHo(null);
    form.resetFields();
  }, []);
  useEffect(() => {
    if (chiTietDonViThuHo?.file_qrcode) {
      setImageUrl(env.VITE_BASE_URL + chiTietDonViThuHo.file_qrcode + "&thumbnail=1");
      console.log("imageUrl", imageUrl);
    } else {
      setImageUrl(null);
    }
  }, [chiTietDonViThuHo]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.ICapNhatDonViThuHoParams = form.getFieldsValue(); //lấy ra values của form

      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietDonViThuHo;
      console.log("values", values);
      await capNhatChiTietDonViThuHo(values, isEditMode); //cập nhật lại DonViThuHo
      await getListDonViThuHo(); //lấy lại danh sách DonViThuHo
      closeModal();
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  // const handleChonFile = useCallback(() => {
  //   if (modalQuanLyFileRef.current?.open) {
  //     modalQuanLyFileRef.current.open();
  //   }
  // }, []);
  const handleChonFile = useCallback(() => {
    if (modalChonQRCodeRef.current?.open) {
      modalChonQRCodeRef.current.open();
    }
  }, []);

  /**
   * Xử lý khi chọn file QR Code từ modal con
   */
  const handleFileQRCodeSuccess = useCallback(
    (filesSelected: any[]) => {
      console.log("handleFileQRCodeSuccess từ modal cha", filesSelected);
      if (filesSelected && filesSelected.length > 0) {
        const selectedFile = filesSelected[0];
        const fileId = selectedFile.id;
        const fileName = selectedFile.ten_alias || selectedFile.ten || "File không tên";
        console.log("selectedFile từ modal cha", selectedFile);
        // Set giá trị vào form của modal cha
        form.setFieldValue("file_qrcode", selectedFile.url_file);
        console.log("selectedFile từ modal cha", selectedFile);

        if (fileId) {
          form.setFieldValue("id_file_qrcode", Number(fileId));

          // Hiển thị hình ảnh nếu là file ảnh
          if (selectedFile.url_file && selectedFile.extension) {
            const imageExtensions = [".jpg", ".jpeg", ".png", ".gif"];
            if (imageExtensions.includes(selectedFile.extension.toLowerCase())) {
              const imageUrl = env.VITE_BASE_URL + selectedFile.url_file + "&thumbnail=1";
              console.log("imageUrl", imageUrl);
              setImageUrl(imageUrl);
            }
          }

          // Đóng modal con sau khi chọn thành công
          if (modalChonQRCodeRef.current?.close) {
            modalChonQRCodeRef.current.close();
          }
        }
      }
    },
    [form],
  );
  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        {/* nút bên trái upload qr */}

        <Button type="primary" onClick={handleChonFile} className="mr-2" icon={<FolderOpenOutlined />}>
          Chọn file QR
        </Button>
        <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName=""
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );

  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietDonViThuHo ? true : false})}
        {renderFormColum({...ten}, 10)}
        {renderFormColum({...ten_tat}, 10)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...dchi}, 16)}
        {renderFormColum({...dthoai}, 8)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...ten_e}, 8)}
        {renderFormColum({...mst}, 6)}
        {renderFormColum({...stt})}
        {renderFormColum({...trang_thai, options: TRANG_THAI_TAO_MOI_DON_VI_THU_HO}, 6)}
      </Row>
      <div className="section-wrapper">
        <div className="section-title">Cấu hình tài khoản thu hộ</div>
        <Row gutter={16}>
          {renderFormColum({...ma_nh, options: listNganHang}, 8)}
          {renderFormColum({...so_tk}, 8)}
          {renderFormColum({...ten_tk}, 8)}
          {renderFormColum({...file_qrcode})}
          {renderFormColum({...id_file_qrcode})}
        </Row>
        {/* ô chọn ảnh để mở lên modalfile */}
        {/* <Button type="primary" icon={<FolderOpenOutlined />} style={{width: "40px"}} onClick={handleChonFile} title="Chọn file từ hệ thống" /> */}
        {/* {renderChonFile()} */}
      </div>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietDonViThuHo ? `${chiTietDonViThuHo.ten}` : "Tạo mới đơn vị thu hộ bhxh"}
            trang_thai_ten={chiTietDonViThuHo?.trang_thai_ten}
            trang_thai={chiTietDonViThuHo?.trang_thai}
          />
        }
        // centered
        className="modal-chi-tiet-don-vi-thu-ho"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={800}
        style={{
          top: 30,
        }}
        styles={
          {
            // body: {
            //   paddingTop: "8px",
            //   paddingBottom: "16px",
            // },
          }
        }
        footer={renderFooter}>
        {renderForm()}
        <ModalChonQRCode ref={modalChonQRCodeRef} chiTietDonViThuHo={chiTietDonViThuHo} onClickChonFileSuccess={handleFileQRCodeSuccess} />
        {/* <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} /> */}
      </Modal>
    </Flex>
  );
});

ModalChiTietDonViThuHoComponent.displayName = "ModalChiTietDonViThuHoComponent";
export const ModalChiTietDonViThuHo = memo(ModalChiTietDonViThuHoComponent, isEqual);
